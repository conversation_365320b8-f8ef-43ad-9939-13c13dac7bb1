:global(.vds-captions) {
  --media-user-font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
  --media-user-font-size: 1.1;
  --media-user-text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8), 0 0 4px rgba(0, 0, 0, 0.5);
  --media-cue-bg: rgba(0, 0, 0, 0.85);
  --media-cue-border-radius: 6px;
  --media-cue-backdrop: blur(12px);
  --media-cue-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}
:global(.vds-captions[data-example]) {
  opacity: 1 !important;
  visibility: visible !important;
}
:global(.vds-captions:not([aria-hidden="true"])) {
  opacity: 1 !important;
  visibility: visible !important;
}
